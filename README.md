# TikTok 智能推流优化系统

## 项目背景

本项目旨在为中国大陆用户提供TikTok直播推流的智能优化服务，通过Cloudflare Worker提供推流码管理、网络优化建议和智能配置，大大简化用户的推流设置过程。

## 架构设计

### 整体架构
```
[用户OBS] -> [用户网络/VPN] -> [TikTok官方服务器]
     ^                              ^
     |                              |
[Electron客户端] -> [Cloudflare Worker] -> [智能优化建议]
```

### 核心组件

1. **Cloudflare Worker (智能管理层)**
   - 用户认证和授权管理
   - 推流码智能管理和验证
   - 网络路径优化分析
   - TikTok服务器智能选择
   - 推流质量监控和诊断
   - RESTful API接口

2. **智能优化引擎**
   - 用户网络环境检测
   - 最佳TikTok服务器推荐
   - 推流参数自动优化
   - 网络问题诊断和建议

3. **数据存储**
   - Cloudflare KV: 用户配置和推流码管理
   - Cloudflare D1: 用户信息和使用统计
   - 网络质量数据分析

## 功能特性

- ✅ 智能推流码管理，支持多用户多地区
- ✅ 网络环境自动检测和优化建议
- ✅ 最佳TikTok服务器智能推荐
- ✅ 推流参数自动优化配置
- ✅ 实时推流状态监控和诊断
- ✅ 简单易用的API接口
- ✅ 安全的用户认证机制
- ✅ 详细的网络质量分析和统计
- ✅ 一键生成OBS推流配置
- ✅ 推流问题智能诊断和解决方案

## 快速开始

### 1. 环境准备
```bash
npm install
npm install -g wrangler
```

### 2. 配置Cloudflare
```bash
wrangler login
wrangler kv:namespace create "USERS"
wrangler kv:namespace create "STREAM_CONFIGS"
wrangler d1 create tiktok-proxy-db
```

### 3. 部署Worker
```bash
npm run deploy
```

### 4. 部署中转服务器
```bash
cd proxy-server
npm install
npm run build
# 部署到你的服务器
```

## API接口

### 用户认证
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "user123",
  "password": "password123"
}
```

### 获取推流配置
```http
GET /api/stream/config
Authorization: Bearer <token>
```

### 更新推流码
```http
POST /api/stream/update
Authorization: Bearer <token>
Content-Type: application/json

{
  "streamKey": "live_xxx_xxx",
  "region": "us-east",
  "platform": "tiktok"
}
```

## 使用方式

### 1. 客户端集成
在你的Electron客户端中集成API调用：

```javascript
// 获取中转推流地址
const response = await fetch('https://your-worker.workers.dev/api/stream/config', {
  headers: {
    'Authorization': `Bearer ${userToken}`
  }
});

const config = await response.json();
// config.proxyUrl 就是中转服务器地址
// 用户在OBS中使用这个地址进行推流
```

### 2. OBS配置
- 服务器: `rtmp://proxy-server.example.com/live`
- 推流码: 通过API获取的代理推流码

## 部署指南

### Cloudflare Worker部署
1. 修改 `wrangler.toml` 中的配置
2. 运行 `npm run deploy`

### 中转服务器部署
1. 选择合适的VPS提供商（建议：Vultr、DigitalOcean、AWS）
2. 部署在不同地区以提供最佳性能
3. 配置Cloudflare Tunnel确保安全连接

## 监控和维护

- 实时监控面板: `https://your-worker.workers.dev/dashboard`
- 日志查看: Cloudflare Dashboard
- 性能指标: 内置统计API

## 安全考虑

- 所有API调用都需要JWT认证
- 推流码加密存储
- 定期轮换访问令牌
- 流量限制和DDoS防护

## 技术栈

- **前端**: Cloudflare Worker (TypeScript)
- **后端**: Node.js RTMP代理服务器
- **数据库**: Cloudflare D1 + KV
- **部署**: Cloudflare Workers + 自建VPS
- **监控**: Cloudflare Analytics

## 贡献指南

1. Fork 本项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue或联系开发者。
