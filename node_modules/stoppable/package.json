{"name": "stoppable", "version": "1.1.0", "engines": {"node": ">=4", "npm": ">=6"}, "keywords": ["server", "net", "connect", "socket", "connection", "stop", "close", "disconnect", "disconnection", "http", "https", "tcp"], "files": ["lib"], "repository": {"type": "git", "url": "git+https://github.com/hunterloftis/stoppable.git"}, "homepage": "https://github.com/hunterloftis/stoppable", "scripts": {"lint": "standard --fix \"lib/**/*.js\" \"test/**/*.js\"", "spec": "nyc --check-coverage mocha --bail \"test/*.test.js\"", "test": "npm run lint && npm audit && npm run spec", "coverage": "nyc mocha --bail \"test/*.test.js\"", "perf:baseline": "node test/performance.js & sleep 2 && artillery quick -d 10 -r 1000 -o /dev/null -k http://localhost:8000", "perf:stoppable": "node test/performance.js 1 & sleep 2 && artillery quick -d 10 -r 1000 -o /dev/null -k http://localhost:8000"}, "main": "lib/stoppable.js", "license": "MIT", "devDependencies": {"artillery": "^1.6.0-15", "awaiting": "^3.0.0", "chai": "^4.1.2", "mocha": "^5.0.5", "nyc": "^11.6.0", "requisition": "^1.7.0", "standard": "^11.0.1"}}