name = "tiktok-proxy-worker"
main = "src/index.ts"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# 环境变量
[vars]
JWT_SECRET = "your-jwt-secret-key-change-this"
ADMIN_PASSWORD = "admin123"

# KV命名空间
[[kv_namespaces]]
binding = "USERS"
id = "your-users-kv-namespace-id"
preview_id = "your-users-kv-preview-id"

[[kv_namespaces]]
binding = "STREAM_CONFIGS"
id = "your-stream-configs-kv-namespace-id"
preview_id = "your-stream-configs-kv-preview-id"



# D1数据库
[[d1_databases]]
binding = "DB"
database_name = "tiktok-proxy-db"
database_id = "your-database-id"

# Durable Objects (用于实时状态管理)
[[durable_objects.bindings]]
name = "STREAM_MONITOR"
class_name = "StreamMonitor"

[[migrations]]
tag = "v1"
new_classes = ["StreamMonitor"]

# 路由配置
[triggers]
crons = ["0 */5 * * * *"]  # 每5分钟检查服务器状态

# 开发环境配置
[env.development]
vars = { JWT_SECRET = "dev-secret", ADMIN_PASSWORD = "dev123" }

# 生产环境配置
[env.production]
vars = { JWT_SECRET = "prod-secret-change-this", ADMIN_PASSWORD = "secure-admin-password" }
