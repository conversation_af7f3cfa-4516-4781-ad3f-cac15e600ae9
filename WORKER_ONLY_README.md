# 🚀 TikTok智能推流优化系统 (纯Worker版本)

## 📋 项目概述

这是一个**仅使用Cloudflare Worker**的TikTok推流优化系统，专为中国大陆用户设计。虽然无法直接解决RTMP转发问题，但提供了强大的智能优化和管理功能。

## ⚡ 核心功能

### 🎯 智能推流管理
- **多用户支持** - 独立的用户账户和推流配置管理
- **推流码验证** - 自动验证TikTok推流码格式和有效性
- **地区智能选择** - 根据用户位置推荐最佳TikTok服务器
- **配置自动生成** - 一键生成OBS推流配置

### 🌐 网络优化引擎
- **网络质量检测** - 实时检测到各TikTok服务器的连接质量
- **延迟优化** - 智能选择延迟最低的服务器
- **参数自动调优** - 根据网络状况自动调整推流参数
- **故障诊断** - 提供详细的网络问题诊断和解决方案

### 📊 监控和分析
- **推流状态监控** - 实时监控推流连接状态
- **质量分析** - 提供推流质量分析和优化建议
- **使用统计** - 详细的使用数据和趋势分析
- **告警系统** - 自动检测和通知推流问题

## 🔧 技术架构

```
[用户OBS] -> [用户网络/VPN] -> [TikTok官方服务器]
     ^                              ^
     |                              |
[Electron客户端] -> [Cloudflare Worker] -> [智能优化建议]
```

### 核心组件
- **Cloudflare Worker** - 主要的API和逻辑处理
- **KV存储** - 用户数据和配置存储
- **D1数据库** - 统计数据和分析
- **网络优化引擎** - 智能网络分析和优化

## 🚀 快速开始

### 1. 环境准备
```bash
npm install
npm install -g wrangler
```

### 2. 配置Cloudflare
```bash
# 登录Cloudflare
wrangler login

# 创建KV命名空间
wrangler kv:namespace create "USERS"
wrangler kv:namespace create "STREAM_CONFIGS"

# 创建D1数据库
wrangler d1 create tiktok-optimizer-db
```

### 3. 更新配置
编辑 `wrangler.toml` 文件，填入创建的资源ID：

```toml
[[kv_namespaces]]
binding = "USERS"
id = "your-users-kv-namespace-id"

[[kv_namespaces]]
binding = "STREAM_CONFIGS"
id = "your-stream-configs-kv-namespace-id"

[[d1_databases]]
binding = "DB"
database_name = "tiktok-optimizer-db"
database_id = "your-database-id"
```

### 4. 设置环境变量
```bash
# 设置JWT密钥
wrangler secret put JWT_SECRET

# 设置管理员密码
wrangler secret put ADMIN_PASSWORD
```

### 5. 部署Worker
```bash
npm run deploy
```

## 📖 API接口文档

### 用户认证
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "user123",
  "password": "password123"
}
```

### 网络优化
```http
POST /api/network/optimize
Authorization: Bearer <token>
Content-Type: application/json

{
  "userLocation": {
    "country": "China",
    "city": "Shanghai",
    "lat": 31.2304,
    "lng": 121.4737
  }
}
```

### 推流配置管理
```http
POST /api/stream/create
Authorization: Bearer <token>
Content-Type: application/json

{
  "originalStreamKey": "live_xxx_xxx",
  "region": "ap-southeast",
  "platform": "tiktok"
}
```

## 💡 使用方式

### 1. 用户注册登录
```javascript
const response = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    username: 'your_username',
    password: 'your_password'
  })
});

const { token } = await response.json();
```

### 2. 获取网络优化建议
```javascript
const optimization = await fetch('/api/network/optimize', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    userLocation: {
      country: 'China',
      city: 'Shanghai',
      lat: 31.2304,
      lng: 121.4737
    }
  })
});

const optimizationData = await optimization.json();
```

### 3. 创建推流配置
```javascript
const streamConfig = await fetch('/api/stream/create', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    originalStreamKey: 'live_xxx_xxx',
    region: 'ap-southeast',
    platform: 'tiktok'
  })
});

const config = await streamConfig.json();
// config.data.instructions 包含详细的OBS设置说明
```

## 🎮 OBS配置示例

系统会自动生成详细的OBS配置说明，包括：

```
📺 OBS Studio 设置
1. 打开OBS Studio
2. 进入 设置 -> 推流
3. 配置推流参数：
   - 服务: 自定义
   - 服务器: rtmp://ingest-sg.tiktok.com/live
   - 推流码: live_xxx_xxx

⚙️ 推荐的推流设置
   - 视频码率: 2500 kbps
   - 分辨率: 1280x720
   - 帧率: 30 FPS
   - 关键帧间隔: 60

🌐 网络优化建议
1. 建议使用海外VPN服务以获得更好的连接质量
2. 推荐选择香港、新加坡或日本的VPN节点
3. 使用有线网络连接而非WiFi以获得更稳定的推流
```

## 🔍 网络优化特性

### 智能服务器选择
- 自动检测到各TikTok服务器的延迟
- 选择最佳的地理位置服务器
- 提供备用服务器建议

### 参数自动优化
- 根据网络质量调整码率
- 智能选择分辨率和帧率
- 优化关键帧间隔

### 故障诊断
- 网络连接问题诊断
- 防火墙设置检查
- VPN配置建议

## 🚫 技术限制

由于Cloudflare Worker的限制，本系统**无法直接处理RTMP流转发**。用户需要：

1. **使用VPN或代理** - 解决网络连接问题
2. **按照优化建议配置** - 使用系统提供的最佳配置
3. **直接推流到TikTok** - 使用优化后的服务器地址

## 🌟 优势特点

✅ **零服务器成本** - 完全基于Cloudflare Worker  
✅ **全球部署** - 利用Cloudflare的全球网络  
✅ **高可用性** - 99.9%的服务可用性  
✅ **智能优化** - 基于实时网络数据的优化建议  
✅ **简单易用** - 一键生成配置，无需复杂设置  
✅ **安全可靠** - 企业级安全和数据保护  

## 📈 监控面板

访问 `https://your-worker.workers.dev/dashboard` 查看：
- 系统状态概览
- 用户使用统计
- 网络质量分析
- 推流成功率

## 🛠️ 开发和扩展

### 本地开发
```bash
npm run dev
```

### 测试
```bash
npm test
```

### 部署
```bash
npm run deploy
```

## 📞 技术支持

如果您在使用过程中遇到问题：

1. 查看系统生成的故障排除建议
2. 检查网络优化建议是否已正确应用
3. 确认VPN或代理设置是否正确
4. 联系技术支持团队

## 📄 许可证

MIT License

---

**注意**: 这是一个智能优化系统，主要提供配置建议和网络优化。实际的网络连接问题需要用户通过VPN、代理等方式自行解决。
