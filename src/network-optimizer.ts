import { Env, TikTokRegion, ApiError } from './types';

interface NetworkTest {
  region: TikTokRegion;
  server: string;
  latency: number;
  packetLoss: number;
  bandwidth: number;
  isReachable: boolean;
}

interface OptimizationResult {
  recommendedRegion: TikTokRegion;
  recommendedServer: string;
  optimizedSettings: {
    bitrate: number;
    resolution: string;
    fps: number;
    keyframeInterval: number;
  };
  networkAdvice: string[];
  troubleshooting: string[];
}

export class NetworkOptimizer {
  constructor(private env: Env) {}

  /**
   * 检测用户网络环境并提供优化建议
   */
  async optimizeForUser(
    userLocation?: { country: string; city: string; lat: number; lng: number },
    userAgent?: string
  ): Promise<OptimizationResult> {
    // 测试所有TikTok服务器的连接质量
    const networkTests = await this.performNetworkTests(userLocation);
    
    // 选择最佳服务器
    const bestServer = this.selectBestServer(networkTests);
    
    // 生成优化设置
    const optimizedSettings = this.generateOptimizedSettings(bestServer);
    
    // 生成网络建议
    const networkAdvice = this.generateNetworkAdvice(networkTests, userLocation);
    
    // 生成故障排除建议
    const troubleshooting = this.generateTroubleshooting(networkTests);

    return {
      recommendedRegion: bestServer.region,
      recommendedServer: bestServer.server,
      optimizedSettings,
      networkAdvice,
      troubleshooting
    };
  }

  /**
   * 获取TikTok服务器列表
   */
  getTikTokServers(): Record<TikTokRegion, string[]> {
    return {
      'us-east': [
        'ingest.tiktok.com',
        'ingest-east.tiktok.com',
        'live-ingest-us-east.tiktok.com'
      ],
      'us-west': [
        'ingest-west.tiktok.com',
        'live-ingest-us-west.tiktok.com'
      ],
      'eu-west': [
        'ingest-eu.tiktok.com',
        'live-ingest-eu.tiktok.com',
        'ingest-london.tiktok.com'
      ],
      'ap-southeast': [
        'ingest-sg.tiktok.com',
        'live-ingest-ap-southeast.tiktok.com',
        'ingest-singapore.tiktok.com'
      ],
      'ap-northeast': [
        'ingest-jp.tiktok.com',
        'live-ingest-ap-northeast.tiktok.com',
        'ingest-tokyo.tiktok.com'
      ],
      'me-south': [
        'ingest-me.tiktok.com',
        'live-ingest-me.tiktok.com'
      ]
    };
  }

  /**
   * 执行网络测试
   */
  private async performNetworkTests(
    userLocation?: { country: string; city: string; lat: number; lng: number }
  ): Promise<NetworkTest[]> {
    const servers = this.getTikTokServers();
    const tests: NetworkTest[] = [];

    for (const [region, serverList] of Object.entries(servers)) {
      for (const server of serverList) {
        try {
          const test = await this.testServerConnection(region as TikTokRegion, server);
          tests.push(test);
        } catch (error) {
          // 如果测试失败，记录为不可达
          tests.push({
            region: region as TikTokRegion,
            server,
            latency: 9999,
            packetLoss: 100,
            bandwidth: 0,
            isReachable: false
          });
        }
      }
    }

    return tests;
  }

  /**
   * 测试单个服务器连接
   */
  private async testServerConnection(region: TikTokRegion, server: string): Promise<NetworkTest> {
    const startTime = Date.now();
    
    try {
      // 使用HTTP请求测试连接性（简化版本）
      const response = await fetch(`https://${server}`, {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000)
      });

      const latency = Date.now() - startTime;
      const isReachable = response.status < 500;

      return {
        region,
        server,
        latency,
        packetLoss: isReachable ? 0 : 100,
        bandwidth: this.estimateBandwidth(latency, isReachable),
        isReachable
      };
    } catch (error) {
      return {
        region,
        server,
        latency: 9999,
        packetLoss: 100,
        bandwidth: 0,
        isReachable: false
      };
    }
  }

  /**
   * 选择最佳服务器
   */
  private selectBestServer(tests: NetworkTest[]): NetworkTest {
    // 过滤可达的服务器
    const reachableTests = tests.filter(test => test.isReachable);
    
    if (reachableTests.length === 0) {
      throw new ApiError('没有可用的TikTok服务器', 503);
    }

    // 按延迟和丢包率排序
    return reachableTests.sort((a, b) => {
      const scoreA = a.latency + (a.packetLoss * 100);
      const scoreB = b.latency + (b.packetLoss * 100);
      return scoreA - scoreB;
    })[0];
  }

  /**
   * 生成优化设置
   */
  private generateOptimizedSettings(bestServer: NetworkTest): {
    bitrate: number;
    resolution: string;
    fps: number;
    keyframeInterval: number;
  } {
    let bitrate = 2500; // 默认2.5Mbps
    let resolution = '1280x720';
    let fps = 30;

    // 根据网络质量调整设置
    if (bestServer.latency > 200) {
      bitrate = 1500; // 降低码率
      fps = 25; // 降低帧率
    } else if (bestServer.latency < 50) {
      bitrate = 4000; // 提高码率
      resolution = '1920x1080';
    }

    if (bestServer.packetLoss > 5) {
      bitrate = Math.max(1000, bitrate * 0.7); // 进一步降低码率
    }

    return {
      bitrate,
      resolution,
      fps,
      keyframeInterval: fps * 2 // 2秒关键帧间隔
    };
  }

  /**
   * 生成网络建议
   */
  private generateNetworkAdvice(
    tests: NetworkTest[], 
    userLocation?: { country: string; city: string; lat: number; lng: number }
  ): string[] {
    const advice: string[] = [];
    const reachableTests = tests.filter(test => test.isReachable);
    const avgLatency = reachableTests.reduce((sum, test) => sum + test.latency, 0) / reachableTests.length;

    if (avgLatency > 300) {
      advice.push('网络延迟较高，建议使用有线网络连接');
      advice.push('考虑使用网络加速器或VPN服务');
    }

    if (reachableTests.length < tests.length * 0.5) {
      advice.push('部分TikTok服务器无法访问，建议使用VPN或代理服务');
      advice.push('检查防火墙设置，确保允许RTMP连接');
    }

    if (userLocation?.country === 'China') {
      advice.push('建议使用海外VPN服务以获得更好的连接质量');
      advice.push('推荐选择香港、新加坡或日本的VPN节点');
    }

    advice.push('使用有线网络连接而非WiFi以获得更稳定的推流');
    advice.push('关闭其他占用带宽的应用程序');
    advice.push('在网络使用较少的时间段进行直播');

    return advice;
  }

  /**
   * 生成故障排除建议
   */
  private generateTroubleshooting(tests: NetworkTest[]): string[] {
    const troubleshooting: string[] = [];
    const reachableTests = tests.filter(test => test.isReachable);

    if (reachableTests.length === 0) {
      troubleshooting.push('无法连接到任何TikTok服务器：');
      troubleshooting.push('1. 检查网络连接是否正常');
      troubleshooting.push('2. 确认防火墙没有阻止RTMP连接');
      troubleshooting.push('3. 尝试使用VPN或代理服务');
      troubleshooting.push('4. 联系网络服务提供商');
    } else if (reachableTests.length < tests.length * 0.3) {
      troubleshooting.push('大部分TikTok服务器无法访问：');
      troubleshooting.push('1. 当前网络可能存在限制');
      troubleshooting.push('2. 建议使用VPN服务');
      troubleshooting.push('3. 尝试更换网络环境');
    }

    const highLatencyTests = reachableTests.filter(test => test.latency > 500);
    if (highLatencyTests.length > 0) {
      troubleshooting.push('网络延迟过高的解决方案：');
      troubleshooting.push('1. 使用有线网络连接');
      troubleshooting.push('2. 关闭其他网络应用');
      troubleshooting.push('3. 选择地理位置更近的VPN节点');
      troubleshooting.push('4. 降低推流码率和分辨率');
    }

    return troubleshooting;
  }

  /**
   * 估算带宽
   */
  private estimateBandwidth(latency: number, isReachable: boolean): number {
    if (!isReachable) return 0;
    
    // 简化的带宽估算（实际应该通过下载测试）
    if (latency < 50) return 10000; // 10Mbps
    if (latency < 100) return 5000; // 5Mbps
    if (latency < 200) return 2000; // 2Mbps
    return 1000; // 1Mbps
  }
}
