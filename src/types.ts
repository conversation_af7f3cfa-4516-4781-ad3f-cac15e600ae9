// 用户相关类型定义
export interface User {
  id: string;
  username: string;
  passwordHash: string;
  email?: string;
  createdAt: string;
  lastLoginAt?: string;
  isActive: boolean;
  plan: 'free' | 'premium' | 'enterprise';
  maxStreams: number;
}

// 推流配置类型
export interface StreamConfig {
  id: string;
  userId: string;
  originalStreamKey: string;
  proxyStreamKey: string;
  region: TikTokRegion;
  platform: 'tiktok' | 'other';
  isActive: boolean;
  createdAt: string;
  lastUsedAt?: string;
  proxyServerId?: string;
}

// TikTok地区类型
export type TikTokRegion = 
  | 'us-east'      // 美国东部
  | 'us-west'      // 美国西部
  | 'eu-west'      // 欧洲西部
  | 'ap-southeast' // 亚太东南（新加坡）
  | 'ap-northeast' // 亚太东北（日本）
  | 'me-south';    // 中东南部

// 代理服务器信息
export interface ProxyServer {
  id: string;
  region: TikTokRegion;
  host: string;
  port: number;
  rtmpUrl: string;
  isHealthy: boolean;
  lastHealthCheck: string;
  currentLoad: number;
  maxCapacity: number;
  location: {
    country: string;
    city: string;
    coordinates: [number, number];
  };
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

// JWT载荷类型
export interface JWTPayload {
  userId: string;
  username: string;
  plan: string;
  iat: number;
  exp: number;
}

// 推流统计信息
export interface StreamStats {
  streamId: string;
  userId: string;
  startTime: string;
  endTime?: string;
  duration: number;
  bytesTransferred: number;
  avgBitrate: number;
  peakBitrate: number;
  dropFrames: number;
  proxyServerId: string;
  region: TikTokRegion;
}

// 服务器健康检查结果
export interface HealthCheckResult {
  serverId: string;
  isHealthy: boolean;
  responseTime: number;
  lastCheck: string;
  error?: string;
}

// 请求类型定义
export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  password: string;
  email?: string;
}

export interface UpdateStreamRequest {
  streamKey: string;
  region: TikTokRegion;
  platform?: 'tiktok' | 'other';
}

export interface CreateStreamRequest {
  originalStreamKey: string;
  region: TikTokRegion;
  platform: 'tiktok' | 'other';
}

// Cloudflare Worker环境类型
export interface Env {
  // KV命名空间
  USERS: KVNamespace;
  STREAM_CONFIGS: KVNamespace;

  // D1数据库
  DB: D1Database;

  // Durable Objects
  STREAM_MONITOR: DurableObjectNamespace;

  // 环境变量
  JWT_SECRET: string;
  ADMIN_PASSWORD: string;
}

// Durable Object状态
export interface StreamMonitorState {
  activeStreams: Map<string, StreamSession>;
  serverStats: Map<string, ServerStats>;
}

export interface StreamSession {
  streamId: string;
  userId: string;
  proxyServerId: string;
  startTime: string;
  lastActivity: string;
  bytesTransferred: number;
  isActive: boolean;
}

export interface ServerStats {
  serverId: string;
  activeStreams: number;
  totalBandwidth: number;
  cpuUsage: number;
  memoryUsage: number;
  lastUpdate: string;
}

// 错误类型
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// 常量定义
export const REGIONS: Record<TikTokRegion, string> = {
  'us-east': '美国东部',
  'us-west': '美国西部',
  'eu-west': '欧洲西部',
  'ap-southeast': '亚太东南',
  'ap-northeast': '亚太东北',
  'me-south': '中东南部'
};

export const DEFAULT_PROXY_SERVERS: ProxyServer[] = [
  {
    id: 'us-east-1',
    region: 'us-east',
    host: 'us-east.proxy.example.com',
    port: 1935,
    rtmpUrl: 'rtmp://us-east.proxy.example.com/live',
    isHealthy: true,
    lastHealthCheck: new Date().toISOString(),
    currentLoad: 0,
    maxCapacity: 100,
    location: {
      country: 'United States',
      city: 'New York',
      coordinates: [40.7128, -74.0060]
    }
  },
  {
    id: 'ap-southeast-1',
    region: 'ap-southeast',
    host: 'sg.proxy.example.com',
    port: 1935,
    rtmpUrl: 'rtmp://sg.proxy.example.com/live',
    isHealthy: true,
    lastHealthCheck: new Date().toISOString(),
    currentLoad: 0,
    maxCapacity: 100,
    location: {
      country: 'Singapore',
      city: 'Singapore',
      coordinates: [1.3521, 103.8198]
    }
  }
];
