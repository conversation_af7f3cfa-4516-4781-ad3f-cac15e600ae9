import { Env, ApiResponse, ApiError } from './types';
import { AuthService } from './auth';
import { StreamService } from './stream';
import { MonitoringService } from './monitoring';
import { NetworkOptimizer } from './network-optimizer';

export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    try {
      const url = new URL(request.url);
      const path = url.pathname;
      const method = request.method;

      // CORS处理
      if (method === 'OPTIONS') {
        return handleCORS();
      }

      // 路由处理
      const response = await handleRequest(request, env, path, method);
      
      // 添加CORS头
      return addCORSHeaders(response);
    } catch (error) {
      console.error('Worker error:', error);
      return createErrorResponse(error instanceof ApiError ? error : new ApiError('Internal Server Error'));
    }
  },

  async scheduled(event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void> {
    // 定时任务：健康检查和清理
    try {
      const monitoringService = new MonitoringService(env);
      await monitoringService.performHealthChecks();
      await monitoringService.cleanupExpiredSessions();
    } catch (error) {
      console.error('Scheduled task error:', error);
    }
  }
};

async function handleRequest(request: Request, env: Env, path: string, method: string): Promise<Response> {
  const authService = new AuthService(env);
  const streamService = new StreamService(env);
  const monitoringService = new MonitoringService(env);
  const networkOptimizer = new NetworkOptimizer(env);

  // 公开路由（无需认证）
  if (path === '/api/health') {
    return createSuccessResponse({ status: 'healthy', timestamp: new Date().toISOString() });
  }

  if (path === '/api/auth/login' && method === 'POST') {
    const body = await request.json() as { username: string; password: string };
    const result = await authService.login(body.username, body.password);
    return createSuccessResponse(result);
  }

  if (path === '/api/auth/register' && method === 'POST') {
    const body = await request.json() as { username: string; password: string; email?: string };
    const result = await authService.register(body.username, body.password, body.email);
    return createSuccessResponse(result);
  }

  // 需要认证的路由
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new ApiError('Unauthorized', 401);
  }

  const token = authHeader.substring(7);
  const user = await authService.verifyToken(token);

  // 用户相关路由
  if (path === '/api/user/profile' && method === 'GET') {
    return createSuccessResponse({ 
      id: user.userId, 
      username: user.username, 
      plan: user.plan 
    });
  }

  // 推流配置路由
  if (path === '/api/stream/config' && method === 'GET') {
    const configs = await streamService.getUserStreamConfigs(user.userId);
    return createSuccessResponse(configs);
  }

  if (path === '/api/stream/create' && method === 'POST') {
    const body = await request.json() as { originalStreamKey: string; region: string; platform?: string };
    const config = await streamService.createStreamConfig(user.userId, body);
    return createSuccessResponse(config);
  }

  if (path === '/api/stream/update' && method === 'POST') {
    const body = await request.json() as { streamId: string; streamKey?: string; region?: string };
    const config = await streamService.updateStreamConfig(user.userId, body.streamId, body);
    return createSuccessResponse(config);
  }

  if (path === '/api/stream/delete' && method === 'DELETE') {
    const url = new URL(request.url);
    const streamId = url.searchParams.get('id');
    if (!streamId) throw new ApiError('Stream ID required', 400);
    
    await streamService.deleteStreamConfig(user.userId, streamId);
    return createSuccessResponse({ message: 'Stream config deleted' });
  }

  // 网络优化路由
  if (path === '/api/network/optimize' && method === 'POST') {
    const body = await request.json() as {
      userLocation?: { country: string; city: string; lat: number; lng: number };
      userAgent?: string;
    };
    const optimization = await networkOptimizer.optimizeForUser(body.userLocation, body.userAgent);
    return createSuccessResponse(optimization);
  }

  if (path === '/api/network/servers' && method === 'GET') {
    const servers = networkOptimizer.getTikTokServers();
    return createSuccessResponse(servers);
  }

  // 监控路由
  if (path === '/api/monitor/stats' && method === 'GET') {
    const stats = await monitoringService.getSystemStats();
    return createSuccessResponse(stats);
  }

  if (path === '/api/monitor/stream-status' && method === 'GET') {
    const url = new URL(request.url);
    const streamId = url.searchParams.get('streamId');
    if (!streamId) throw new ApiError('Stream ID required', 400);
    
    const status = await monitoringService.getStreamStatus(streamId);
    return createSuccessResponse(status);
  }

  // 管理员路由
  if (path.startsWith('/api/admin/')) {
    const adminPassword = request.headers.get('X-Admin-Password');
    if (adminPassword !== env.ADMIN_PASSWORD) {
      throw new ApiError('Admin access denied', 403);
    }

    if (path === '/api/admin/users' && method === 'GET') {
      const users = await authService.getAllUsers();
      return createSuccessResponse(users);
    }

    if (path === '/api/admin/network/test' && method === 'POST') {
      const body = await request.json() as {
        userLocation?: { country: string; city: string; lat: number; lng: number };
      };
      const optimization = await networkOptimizer.optimizeForUser(body.userLocation);
      return createSuccessResponse(optimization);
    }
  }

  // 静态文件和仪表板
  if (path === '/' || path === '/dashboard') {
    return new Response(getDashboardHTML(), {
      headers: { 'Content-Type': 'text/html' }
    });
  }

  throw new ApiError('Not Found', 404);
}

function createSuccessResponse<T>(data: T): Response {
  const response: ApiResponse<T> = {
    success: true,
    data,
    timestamp: new Date().toISOString()
  };
  
  return new Response(JSON.stringify(response), {
    headers: { 'Content-Type': 'application/json' }
  });
}

function createErrorResponse(error: ApiError): Response {
  const response: ApiResponse = {
    success: false,
    error: error.message,
    timestamp: new Date().toISOString()
  };
  
  return new Response(JSON.stringify(response), {
    status: error.statusCode,
    headers: { 'Content-Type': 'application/json' }
  });
}

function handleCORS(): Response {
  return new Response(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Admin-Password',
      'Access-Control-Max-Age': '86400'
    }
  });
}

function addCORSHeaders(response: Response): Response {
  const newResponse = new Response(response.body, response);
  newResponse.headers.set('Access-Control-Allow-Origin', '*');
  newResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  newResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Admin-Password');
  return newResponse;
}

function getDashboardHTML(): string {
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TikTok推流中转服务 - 控制面板</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .card { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .status.healthy { background: #d4edda; color: #155724; }
        .status.unhealthy { background: #f8d7da; color: #721c24; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        input, select { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 TikTok推流中转服务</h1>
            <p>为全球用户提供稳定的TikTok直播推流中转服务</p>
        </div>
        
        <div class="grid">
            <div class="card">
                <h3>📊 系统状态</h3>
                <div id="system-status">加载中...</div>
            </div>
            
            <div class="card">
                <h3>🌍 代理服务器</h3>
                <div id="proxy-servers">加载中...</div>
            </div>
            
            <div class="card">
                <h3>👥 用户管理</h3>
                <div id="user-management">
                    <button onclick="showUsers()">查看用户列表</button>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h3>📖 API文档</h3>
            <p>查看完整的API文档和使用示例</p>
            <button onclick="window.open('/api/docs', '_blank')">查看API文档</button>
        </div>
    </div>
    
    <script>
        // 这里可以添加JavaScript代码来实现动态功能
        async function loadSystemStatus() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                document.getElementById('system-status').innerHTML = 
                    '<span class="status healthy">系统正常</span><br>' +
                    '最后更新: ' + new Date(data.data.timestamp).toLocaleString();
            } catch (error) {
                document.getElementById('system-status').innerHTML = 
                    '<span class="status unhealthy">系统异常</span>';
            }
        }
        
        loadSystemStatus();
        setInterval(loadSystemStatus, 30000); // 每30秒更新一次
    </script>
</body>
</html>`;
}
