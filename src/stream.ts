import { Env, StreamConfig, TikTokRegion, ApiError, CreateStreamRequest, UpdateStreamRequest } from './types';
import { NetworkOptimizer } from './network-optimizer';

export class StreamService {
  private networkOptimizer: NetworkOptimizer;

  constructor(private env: Env) {
    this.networkOptimizer = new NetworkOptimizer(env);
  }

  /**
   * 获取用户的所有推流配置
   */
  async getUserStreamConfigs(userId: string): Promise<StreamConfig[]> {
    const configs: StreamConfig[] = [];
    
    // 从KV存储获取用户的推流配置
    const listResult = await this.env.STREAM_CONFIGS.list({ prefix: `user:${userId}:` });
    
    for (const key of listResult.keys) {
      const configData = await this.env.STREAM_CONFIGS.get(key.name);
      if (configData) {
        const config: StreamConfig = JSON.parse(configData);
        configs.push(config);
      }
    }

    return configs.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  /**
   * 创建新的推流配置
   */
  async createStreamConfig(userId: string, request: CreateStreamRequest): Promise<{
    config: StreamConfig;
    optimizedUrl: string;
    instructions: string;
    optimization: any;
  }> {
    // 检查用户是否已达到最大推流数限制
    const existingConfigs = await this.getUserStreamConfigs(userId);
    const user = await this.getUserInfo(userId);

    if (existingConfigs.length >= user.maxStreams) {
      throw new ApiError(`您的套餐最多支持 ${user.maxStreams} 个推流配置`, 400);
    }

    // 验证推流码格式
    if (!this.validateStreamKey(request.originalStreamKey)) {
      throw new ApiError('推流码格式不正确', 400);
    }

    // 获取网络优化建议
    const optimization = await this.networkOptimizer.optimizeForUser();

    // 构建优化的推流URL
    const optimizedUrl = this.buildOptimizedUrl(request.originalStreamKey, optimization.recommendedRegion);

    // 创建推流配置
    const configId = this.generateConfigId();
    const config: StreamConfig = {
      id: configId,
      userId,
      originalStreamKey: request.originalStreamKey,
      proxyStreamKey: `optimized_${configId}`,
      region: optimization.recommendedRegion,
      platform: request.platform,
      isActive: true,
      createdAt: new Date().toISOString(),
      proxyServerId: optimization.recommendedServer
    };

    // 保存配置
    const configKey = `user:${userId}:config:${configId}`;
    await this.env.STREAM_CONFIGS.put(configKey, JSON.stringify(config));

    return {
      config,
      optimizedUrl,
      instructions: this.generateInstructions(optimizedUrl, config.proxyStreamKey, optimization),
      optimization
    };
  }

  /**
   * 更新推流配置
   */
  async updateStreamConfig(
    userId: string, 
    configId: string, 
    updates: Partial<UpdateStreamRequest>
  ): Promise<StreamConfig> {
    const configKey = `user:${userId}:config:${configId}`;
    const configData = await this.env.STREAM_CONFIGS.get(configKey);
    
    if (!configData) {
      throw new ApiError('推流配置不存在', 404);
    }

    const config: StreamConfig = JSON.parse(configData);
    
    // 如果更新了推流码，需要验证格式
    if (updates.streamKey && !this.validateStreamKey(updates.streamKey)) {
      throw new ApiError('推流码格式不正确', 400);
    }

    // 如果更新了地区，重新优化配置
    if (updates.region && updates.region !== config.region) {
      const optimization = await this.networkOptimizer.optimizeForUser();
      config.proxyServerId = optimization.recommendedServer;
    }

    // 更新配置
    const updatedConfig: StreamConfig = {
      ...config,
      ...updates,
      originalStreamKey: updates.streamKey || config.originalStreamKey,
      region: updates.region || config.region
    };

    await this.env.STREAM_CONFIGS.put(configKey, JSON.stringify(updatedConfig));

    return updatedConfig;
  }

  /**
   * 删除推流配置
   */
  async deleteStreamConfig(userId: string, configId: string): Promise<void> {
    const configKey = `user:${userId}:config:${configId}`;
    const configData = await this.env.STREAM_CONFIGS.get(configKey);

    if (!configData) {
      throw new ApiError('推流配置不存在', 404);
    }

    // 删除配置
    await this.env.STREAM_CONFIGS.delete(configKey);
  }

  /**
   * 通过代理推流码获取原始推流信息
   */
  async getOriginalStreamInfo(proxyStreamKey: string): Promise<{
    originalStreamKey: string;
    userId: string;
    configId: string;
    serverId: string;
  } | null> {
    const mappingData = await this.env.STREAM_CONFIGS.get(`proxy:${proxyStreamKey}`);
    if (!mappingData) return null;

    return JSON.parse(mappingData);
  }

  /**
   * 激活/停用推流配置
   */
  async toggleStreamConfig(userId: string, configId: string, isActive: boolean): Promise<StreamConfig> {
    const configKey = `user:${userId}:config:${configId}`;
    const configData = await this.env.STREAM_CONFIGS.get(configKey);
    
    if (!configData) {
      throw new ApiError('推流配置不存在', 404);
    }

    const config: StreamConfig = JSON.parse(configData);
    config.isActive = isActive;

    await this.env.STREAM_CONFIGS.put(configKey, JSON.stringify(config));

    return config;
  }

  /**
   * 获取推流配置的详细信息（包括优化建议）
   */
  async getStreamConfigDetails(userId: string, configId: string): Promise<{
    config: StreamConfig;
    optimizedUrl: string;
    optimization: any;
    instructions: string;
  }> {
    const configKey = `user:${userId}:config:${configId}`;
    const configData = await this.env.STREAM_CONFIGS.get(configKey);

    if (!configData) {
      throw new ApiError('推流配置不存在', 404);
    }

    const config: StreamConfig = JSON.parse(configData);

    // 获取最新的网络优化建议
    const optimization = await this.networkOptimizer.optimizeForUser();
    const optimizedUrl = this.buildOptimizedUrl(config.originalStreamKey, config.region);

    return {
      config,
      optimizedUrl,
      optimization,
      instructions: this.generateInstructions(optimizedUrl, config.proxyStreamKey, optimization)
    };
  }

  /**
   * 验证推流码格式
   */
  private validateStreamKey(streamKey: string): boolean {
    // TikTok推流码通常格式为: live_xxxxx_xxxxx
    const tiktokPattern = /^live_[a-zA-Z0-9_]+$/;
    
    // 其他平台的推流码格式可以在这里添加
    return tiktokPattern.test(streamKey) || streamKey.length > 10;
  }

  /**
   * 生成配置ID
   */
  private generateConfigId(): string {
    return 'cfg_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 构建优化的推流URL
   */
  private buildOptimizedUrl(originalStreamKey: string, region: TikTokRegion): string {
    const servers = this.networkOptimizer.getTikTokServers();
    const regionServers = servers[region];
    const bestServer = regionServers[0]; // 使用第一个服务器作为默认

    return `rtmp://${bestServer}/live/${originalStreamKey}`;
  }

  /**
   * 生成使用说明
   */
  private generateInstructions(optimizedUrl: string, streamKey: string, optimization?: any): string {
    const serverUrl = optimizedUrl.split('/live/')[0];
    const actualStreamKey = optimizedUrl.split('/live/')[1];

    let instructions = `
## 🚀 TikTok推流优化配置

### 📺 OBS Studio 设置
1. **打开OBS Studio**
2. **进入 设置 -> 推流**
3. **配置推流参数：**
   - **服务**: 自定义
   - **服务器**: ${serverUrl}/live
   - **推流码**: ${actualStreamKey}

### ⚙️ 推荐的推流设置`;

    if (optimization) {
      instructions += `
   - **视频码率**: ${optimization.optimizedSettings.bitrate} kbps
   - **分辨率**: ${optimization.optimizedSettings.resolution}
   - **帧率**: ${optimization.optimizedSettings.fps} FPS
   - **关键帧间隔**: ${optimization.optimizedSettings.keyframeInterval}`;
    }

    instructions += `

### 🌐 网络优化建议`;

    if (optimization?.networkAdvice) {
      optimization.networkAdvice.forEach((advice: string, index: number) => {
        instructions += `\n${index + 1}. ${advice}`;
      });
    }

    if (optimization?.troubleshooting && optimization.troubleshooting.length > 0) {
      instructions += `\n\n### 🔧 故障排除`;
      optimization.troubleshooting.forEach((tip: string) => {
        instructions += `\n- ${tip}`;
      });
    }

    instructions += `

### ⚠️ 重要提醒
- 推流码请勿泄露给他人
- 建议在网络较好的时段进行直播
- 如遇问题请联系技术支持

### 📞 技术支持
如果按照以上设置仍无法正常推流，请联系我们的技术支持团队。
    `.trim();

    return instructions;
  }

  /**
   * 获取用户信息（简化版本）
   */
  private async getUserInfo(userId: string): Promise<{ maxStreams: number }> {
    // 这里应该从用户服务获取完整用户信息
    // 简化处理，返回默认值
    return { maxStreams: 2 };
  }
}
